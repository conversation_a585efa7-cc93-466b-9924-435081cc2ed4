/**
 * Premium styles for Custom Order Form
 * This file will only be included in the premium version of the plugin
 */

/* تنسيق الحقول المتميزة */
.premium-fields {
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-top: 20px;
    border: 1px solid var(--border-color);
}

.premium-fields h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
}

/* تنسيق لوحة التحليلات */
.analytics-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.analytics-card {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    padding: 20px;
    transition: transform 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.analytics-card h3 {
    color: #333;
    font-size: 16px;
    margin-bottom: 15px;
    font-weight: 600;
}

.analytics-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.analytics-chart {
    height: 200px;
    margin-top: 15px;
}

.top-products {
    list-style: none;
    padding: 0;
    margin: 0;
}

.top-products li {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.top-products li:last-child {
    border-bottom: none;
}

.product-name {
    font-weight: 500;
}

.product-sales {
    color: var(--primary-color);
    font-weight: 600;
}

.date-filter {
    margin-top: 20px;
    display: flex;
    align-items: center;
}

.date-filter label {
    margin-right: 10px;
}

.date-filter select {
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

/* تنسيق الإعدادات المتقدمة */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: var(--primary-color);
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

/* تنسيق حقول النموذج المتميزة */
.premium-fields .form-group {
    margin-bottom: 15px;
}

.premium-fields label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.premium-fields textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    resize: vertical;
}

.premium-fields input[type="date"] {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.radio-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.radio-group input[type="radio"] {
    margin-right: 8px;
}

/* تنسيق الأزرار المتميزة */
.premium-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.premium-button:hover {
    background-color: #1e40af;
}

/* شارة المميزات المتميزة */
.premium-badge {
    display: inline-block;
    background-color: #fbbf24;
    color: #7c2d12;
    font-size: 12px;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 20px;
    margin-left: 8px;
}

/* تنسيق الرسائل المتميزة */
.premium-message {
    background-color: #f0f9ff;
    border-left: 4px solid var(--primary-color);
    padding: 15px;
    margin: 20px 0;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.premium-message p {
    margin: 0;
    color: #0c4a6e;
}

/* تنسيق الجداول المتميزة */
.premium-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.premium-table th,
.premium-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.premium-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.premium-table tr:hover {
    background-color: #f8f9fa;
}

/* تنسيق الإشعارات المتميزة */
.premium-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    z-index: 9999;
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.premium-notification.show {
    transform: translateY(0);
    opacity: 1;
}

.premium-notification-icon {
    margin-right: 15px;
    color: var(--primary-color);
    font-size: 24px;
}

.premium-notification-content h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
}

.premium-notification-content p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

.premium-notification-close {
    margin-left: 15px;
    cursor: pointer;
    color: #999;
}

/* تنسيق الرسوم البيانية */
canvas {
    max-width: 100%;
}
