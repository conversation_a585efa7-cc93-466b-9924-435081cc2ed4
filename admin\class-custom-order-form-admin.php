<?php
class Custom_Order_Form_Admin {
    private $plugin_name;
    private $version;

    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('add_meta_boxes', array($this, 'add_cart_button_metabox'));
        add_action('add_meta_boxes', array($this, 'add_variation_images_metabox'));
        add_action('save_post_product', array($this, 'save_cart_button_metabox'));
        add_action('save_post_product', array($this, 'save_variation_images_metabox'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('wp_ajax_save_form_settings', array($this, 'save_form_settings'));
        add_action('wp_ajax_delete_abandoned_order', array($this, 'delete_abandoned_order'));
        add_action('wp_ajax_add_block_item', array($this, 'add_block_item'));
        add_action('wp_ajax_remove_block_item', array($this, 'remove_block_item'));
        add_action('wp_ajax_nopriv_check_customer_block', array($this, 'check_customer_block'));
        add_action('wp_ajax_download_shipping_csv', array($this, 'download_shipping_csv'));
    }

    public function add_block_item() {
        check_ajax_referer('custom_order_form_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء');
            return;
        }

        $type = isset($_POST['type']) ? sanitize_text_field($_POST['type']) : '';
        $value = isset($_POST['value']) ? sanitize_text_field($_POST['value']) : '';
        $reason = isset($_POST['reason']) ? sanitize_text_field($_POST['reason']) : '';

        if (!in_array($type, array('phone', 'ip')) || empty($value)) {
            wp_send_json_error('بيانات غير صالحة');
            return;
        }

        // التحقق من صحة القيمة
        if ($type === 'ip' && !filter_var($value, FILTER_VALIDATE_IP)) {
            wp_send_json_error('عنوان IP غير صالح');
            return;
        }

        $blocked_items = get_option('custom_order_form_blocked_items', array());

        // التحقق من وجود العنصر مسبقاً
        foreach ($blocked_items as $item) {
            if ($item['type'] === $type && $item['value'] === $value) {
                wp_send_json_error('هذا العنصر محظور مسبقاً');
                return;
            }
        }

        // إضافة العنصر الجديد
        $blocked_items[] = array(
            'type' => $type,
            'value' => $value,
            'reason' => $reason,
            'date' => current_time('mysql')
        );

        update_option('custom_order_form_blocked_items', $blocked_items);
        wp_send_json_success('تم إضافة الحظر بنجاح');
    }

    public function remove_block_item() {
        check_ajax_referer('custom_order_form_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء');
            return;
        }

        $type = isset($_POST['type']) ? sanitize_text_field($_POST['type']) : '';
        $value = isset($_POST['value']) ? sanitize_text_field($_POST['value']) : '';

        if (empty($type) || empty($value)) {
            wp_send_json_error('بيانات غير صالحة');
            return;
        }

        $blocked_items = get_option('custom_order_form_blocked_items', array());

        // البحث عن العنصر وحذفه
        foreach ($blocked_items as $key => $item) {
            if ($item['type'] === $type && $item['value'] === $value) {
                unset($blocked_items[$key]);
                update_option('custom_order_form_blocked_items', array_values($blocked_items));
                wp_send_json_success('تم إلغاء الحظر بنجاح');
                return;
            }
        }

        wp_send_json_error('لم يتم العثور على العنصر المحظور');
    }

    /**
     * تنزيل ملف CSV لأسعار التوصيل
     */
    public function download_shipping_csv() {
        // التحقق من الصلاحيات
        if (!current_user_can('manage_options')) {
            wp_die('غير مصرح لك بالوصول إلى هذه الصفحة');
        }

        // الحصول على أسعار التوصيل الحالية
        $shipping_prices = get_option('custom_order_form_shipping_prices', array());

        // إنشاء محتوى ملف CSV
        $csv_content = "الولاية,سعر التوصيل للمنزل,سعر التوصيل للمكتب\n";

        // قائمة الولايات
        $states = array(
            "01: ولاية أدرار", "02: ولاية الشلف", "03: ولاية الأغواط", "04: ولاية أم البواقي", "05: ولاية باتنة",
            "06: ولاية بجاية", "07: ولاية بسكرة", "08: ولاية بشار", "09: ولاية البليدة", "10: ولاية البويرة",
            "11: ولاية تمنراست", "12: ولاية تبسة", "13: ولاية تلمسان", "14: ولاية تيارت", "15: ولاية تيزي وزو",
            "16: ولاية الجزائر", "17: ولاية الجلفة", "18: ولاية جيجل", "19: ولاية سطيف", "20: ولاية سعيدة",
            "21: ولاية سكيكدة", "22: ولاية سيدي بلعباس", "23: ولاية عنابة", "24: ولاية قالمة", "25: ولاية قسنطينة",
            "26: ولاية المدية", "27: ولاية مستغانم", "28: ولاية المسيلة", "29: ولاية معسكر", "30: ولاية ورقلة",
            "31: ولاية وهران", "32: ولاية البيض", "33: ولاية إليزي", "34: ولاية برج بوعريريج", "35: ولاية بومرداس",
            "36: ولاية الطارف", "37: ولاية تندوف", "38: ولاية تيسمسيلت", "39: ولاية الوادي", "40: ولاية خنشلة",
            "41: ولاية سوق أهراس", "42: ولاية تيبازة", "43: ولاية ميلة", "44: ولاية عين الدفلى", "45: ولاية النعامة",
            "46: ولاية عين تموشنت", "47: ولاية غرداية", "48: ولاية غليزان", "49: ولاية تيميمون", "50: ولاية برج باجي مختار",
            "51: ولاية أولاد جلال", "52: ولاية بني عباس", "53: ولاية عين صالح", "54: ولاية عين قزام", "55: ولاية تقرت",
            "56: ولاية جانت", "57: ولاية المغير", "58: ولاية المنيعة"
        );

        // القيم الافتراضية لأسعار التوصيل
        $default_prices = array(
            "01: ولاية أدرار" => array("home" => 1300, "office" => 900),
            "02: ولاية الشلف" => array("home" => 800, "office" => 500),
            "03: ولاية الأغواط" => array("home" => 950, "office" => 600),
            "04: ولاية أم البواقي" => array("home" => 800, "office" => 500),
            "05: ولاية باتنة" => array("home" => 800, "office" => 500),
            "06: ولاية بجاية" => array("home" => 800, "office" => 500),
            "07: ولاية بسكرة" => array("home" => 950, "office" => 700),
            "08: ولاية بشار" => array("home" => 1250, "office" => 700),
            "09: ولاية البليدة" => array("home" => 600, "office" => 400),
            "10: ولاية البويرة" => array("home" => 750, "office" => 500),
            "11: ولاية تمنراست" => array("home" => 1500, "office" => 1050),
            "12: ولاية تبسة" => array("home" => 900, "office" => 500),
            "13: ولاية تلمسان" => array("home" => 850, "office" => 500),
            "14: ولاية تيارت" => array("home" => 800, "office" => 500),
            "15: ولاية تيزي وزو" => array("home" => 850, "office" => 500),
            "16: ولاية الجزائر" => array("home" => 650, "office" => 350),
            "17: ولاية الجلفة" => array("home" => 850, "office" => 500),
            "18: ولاية جيجل" => array("home" => 800, "office" => 500),
            "19: ولاية سطيف" => array("home" => 850, "office" => 500),
            "20: ولاية سعيدة" => array("home" => 850, "office" => 500),
            "21: ولاية سكيكدة" => array("home" => 850, "office" => 500),
            "22: ولاية سيدي بلعباس" => array("home" => 850, "office" => 500),
            "23: ولاية عنابة" => array("home" => 850, "office" => 500),
            "24: ولاية قالمة" => array("home" => 850, "office" => 500),
            "25: ولاية قسنطينة" => array("home" => 850, "office" => 500),
            "26: ولاية المدية" => array("home" => 500, "office" => 300),
            "27: ولاية مستغانم" => array("home" => 850, "office" => 500),
            "28: ولاية المسيلة" => array("home" => 800, "office" => 500),
            "29: ولاية معسكر" => array("home" => 800, "office" => 500),
            "30: ولاية ورقلة" => array("home" => 1000, "office" => 600),
            "31: ولاية وهران" => array("home" => 800, "office" => 500),
            "32: ولاية البيض" => array("home" => 1000, "office" => 600),
            "33: ولاية إليزي" => array("home" => 2000, "office" => 1500),
            "34: ولاية برج بوعريريج" => array("home" => 800, "office" => 500),
            "35: ولاية بومرداس" => array("home" => 800, "office" => 500),
            "36: ولاية الطارف" => array("home" => 850, "office" => 500),
            "37: ولاية تندوف" => array("home" => 2000, "office" => 1800),
            "38: ولاية تيسمسيلت" => array("home" => 750, "office" => 750),
            "39: ولاية الوادي" => array("home" => 950, "office" => 600),
            "40: ولاية خنشلة" => array("home" => 850, "office" => 850),
            "41: ولاية سوق أهراس" => array("home" => 850, "office" => 500),
            "42: ولاية تيبازة" => array("home" => 800, "office" => 500),
            "43: ولاية ميلة" => array("home" => 850, "office" => 500),
            "44: ولاية عين الدفلى" => array("home" => 850, "office" => 500),
            "45: ولاية النعامة" => array("home" => 1200, "office" => 600),
            "46: ولاية عين تموشنت" => array("home" => 850, "office" => 500),
            "47: ولاية غرداية" => array("home" => 950, "office" => 600),
            "48: ولاية غليزان" => array("home" => 850, "office" => 500),
            "49: ولاية تيميمون" => array("home" => 1400, "office" => 1400),
            "50: ولاية برج باجي مختار" => array("home" => 2000, "office" => 2000),
            "51: ولاية أولاد جلال" => array("home" => 950, "office" => 600),
            "52: ولاية بني عباس" => array("home" => 1400, "office" => 1400),
            "53: ولاية عين صالح" => array("home" => 1600, "office" => 1600),
            "54: ولاية عين قزام" => array("home" => 1600, "office" => 1600),
            "55: ولاية تقرت" => array("home" => 950, "office" => 600),
            "56: ولاية جانت" => array("home" => 2000, "office" => 2000),
            "57: ولاية المغير" => array("home" => 950, "office" => 950),
            "58: ولاية المنيعة" => array("home" => 1000, "office" => 1000)
        );

        // دمج الأسعار المخصصة مع الأسعار الافتراضية
        $shipping_prices = !empty($shipping_prices) ? $shipping_prices : $default_prices;

        foreach ($states as $state) {
            $home_price = isset($shipping_prices[$state]['home']) ? $shipping_prices[$state]['home'] : $default_prices[$state]['home'];
            $office_price = isset($shipping_prices[$state]['office']) ? $shipping_prices[$state]['office'] : $default_prices[$state]['office'];

            $csv_content .= $state . ',' . $home_price . ',' . $office_price . "\n";
        }

        // إعداد رأس الملف للتنزيل
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="shipping_prices.csv"');

        // إخراج محتوى الملف
        echo $csv_content;
        exit;
    }

    public function toggle_customer_block() {
        check_ajax_referer('custom_order_form_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء');
            return;
        }

        $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
        $block = isset($_POST['block']) ? (bool)$_POST['block'] : false;

        if (!$phone) {
            wp_send_json_error('رقم الهاتف غير صالح');
            return;
        }

        $blocked_customers = get_option('custom_order_form_blocked_customers', array());

        if ($block) {
            if (!in_array($phone, $blocked_customers)) {
                $blocked_customers[] = $phone;
            }
        } else {
            $blocked_customers = array_diff($blocked_customers, array($phone));
        }

        update_option('custom_order_form_blocked_customers', array_values($blocked_customers));
        wp_send_json_success();
    }

    public function check_customer_block() {
        $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
        $ip = $_SERVER['REMOTE_ADDR'];

        if (!$phone && !$ip) {
            wp_send_json_error('بيانات غير صالحة');
            return;
        }

        $blocked_customers = get_option('custom_order_form_blocked_customers', array());
        $is_blocked = in_array($phone, $blocked_customers);

        if (!$is_blocked) {
            // التحقق من IP
            global $wpdb;
            $blocked_ips = $wpdb->get_col($wpdb->prepare("
                SELECT DISTINCT pm_ip.meta_value
                FROM {$wpdb->postmeta} pm_ip
                JOIN {$wpdb->postmeta} pm_phone ON pm_phone.post_id = pm_ip.post_id
                WHERE pm_ip.meta_key = '_customer_ip_address'
                AND pm_phone.meta_key = '_billing_phone'
                AND pm_phone.meta_value IN ('" . implode("','", $blocked_customers) . "')
            "));

            $is_blocked = in_array($ip, $blocked_ips);
        }

        wp_send_json_success(array('blocked' => $is_blocked));
    }

    public function delete_abandoned_order() {
        check_ajax_referer('custom_order_form_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء');
            return;
        }

        $order_id = isset($_POST['order_id']) ? sanitize_text_field($_POST['order_id']) : '';
        if (!$order_id) {
            wp_send_json_error('معرف الطلب غير صالح');
            return;
        }

        $abandoned_orders = get_option('custom_order_form_abandoned_orders', array());

        // البحث عن الطلب وحذفه
        foreach ($abandoned_orders as $key => $order) {
            if ($order['id'] === $order_id) {
                unset($abandoned_orders[$key]);
                update_option('custom_order_form_abandoned_orders', array_values($abandoned_orders));
                wp_send_json_success('تم حذف الطلب بنجاح');
                return;
            }
        }

        wp_send_json_error('لم يتم العثور على الطلب');
    }

    public function add_admin_menu() {
        add_menu_page(
            'إعدادات فورم الطلب',
            'فورم الطلب',
            'manage_options',
            'custom-order-form',
            array($this, 'display_admin_page'),
            'dashicons-format-aside',
            30
        );
    }

    public function enqueue_admin_assets($hook) {
        if ('toplevel_page_custom-order-form' !== $hook) {
            return;
        }

        // Enqueue styles
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_style('custom-order-form-admin', plugin_dir_url(__FILE__) . 'css/admin.css', array(), $this->version);

        // Enqueue Google Fonts
        wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;600&family=Tajawal:wght@400;500;700&family=Cairo:wght@400;600;700&display=swap', array());

        // Enqueue scripts
        wp_enqueue_script('wp-color-picker');
        wp_enqueue_script('custom-order-form-admin', plugin_dir_url(__FILE__) . 'js/admin.js', array('jquery', 'wp-color-picker'), $this->version, true);
        wp_enqueue_script('settings-preview', plugin_dir_url(__FILE__) . 'js/settings-preview.js', array('jquery', 'wp-color-picker', 'custom-order-form-admin'), $this->version, true);

        // Localize scripts
        $settings = array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('custom_order_form_admin_nonce'),
            'currentSettings' => array(
                'design' => get_option('custom_order_form_design', array(
                    'primaryColor' => '#2563eb',
                    'buttonColor' => '#2563eb',
                    'backgroundColor' => '#ffffff',
                    'textColor' => '#1f2937',
                    'borderColor' => '#e2e8f0',
                    'borderRadius' => '12',
                    'fontFamily' => 'IBM Plex Sans Arabic'
                )),
                'fieldVisibility' => get_option('custom_order_form_field_visibility', array(
                    'show_address' => true,
                    'show_state' => true,
                    'show_municipality' => true
                )),
                'shipping' => get_option('custom_order_form_shipping_settings', array(
                    'fixed_price' => 0,
                    'use_fixed_price' => false
                ))
            )
        );

        wp_localize_script('custom-order-form-admin', 'customOrderFormAdmin', $settings);
        wp_localize_script('settings-preview', 'formPreviewSettings', $settings);
    }

    public function display_admin_page() {
        if (!current_user_can('manage_options')) {
            return;
        }

        $field_labels = get_option('custom_order_form_field_labels', array(
            'fullName' => 'الاسم بالكامل',
            'phone' => 'رقم الهاتف',
            'address' => 'العنوان بالتفصيل',
            'state' => 'الولاية',
            'municipality' => 'البلدية'
        ));

        $form_title = get_option('custom_order_form_title', 'أضف معلوماتك في الأسفل لطلب هذا المنتج');

        $field_visibility = get_option('custom_order_form_field_visibility', array(
            'show_address' => true,
            'show_state' => true,
            'show_municipality' => true
        ));

        $shipping_settings = get_option('custom_order_form_shipping_settings', array(
            'fixed_price' => 0,
            'use_fixed_price' => false
        ));

        // الحصول على أسعار التوصيل المخصصة
        $shipping_prices = get_option('custom_order_form_shipping_prices', array());

        $whatsapp_settings = get_option('custom_order_form_whatsapp_settings', array(
            'number' => '',
            'enabled' => true
        ));

        $button_settings = get_option('custom_order_form_button_settings', array(
            'show_sticky_button' => true,
            'button_text' => 'اشتري الآن'
        ));

        $spam_settings = get_option('custom_order_form_spam_settings', array(
            'disable_autocomplete' => false,
            'disable_copy_paste' => false,
            'limit_orders' => false,
            'save_abandoned' => true
        ));

        $design_settings = get_option('custom_order_form_design', array(
            'primaryColor' => '#2563eb',
            'buttonColor' => '#2563eb',
            'fontFamily' => 'IBM Plex Sans Arabic'
        ));
        ?>
        <div class="wrap custom-order-form-settings">
            <h1>إعدادات فورم الطلب</h1>

            <form id="custom-order-form-settings" method="post">
                <?php wp_nonce_field('custom_order_form_settings', 'custom_order_form_nonce'); ?>

                <div class="settings-tabs">
                    <button type="button" class="settings-tab active" data-tab="fields">الحقول</button>
                    <button type="button" class="settings-tab" data-tab="design">التصميم</button>
                    <button type="button" class="settings-tab" data-tab="shipping">التوصيل</button>
                    <button type="button" class="settings-tab" data-tab="orders">الطلبات</button>
                </div>

                <div class="settings-panel" id="orders-panel">
                    <h2>الطلبات المتروكة</h2>
                    <div class="abandoned-orders">
                        <?php
                        global $wpdb;
                        $abandoned_orders = get_option('custom_order_form_abandoned_orders', array());
                        if (!empty($abandoned_orders)): ?>
                            <table class="wp-list-table widefat fixed striped">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>رقم الهاتف</th>
                                        <th>العنوان</th>
                                        <th>المنتج</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($abandoned_orders as $order): ?>
                                    <tr>
                                        <td><?php echo esc_html($order['fullName'] ?? ''); ?></td>
                                        <td><?php echo esc_html($order['phone'] ?? ''); ?></td>
                                        <td><?php echo esc_html($order['address'] ?? ''); ?></td>
                                        <td><?php echo esc_html($order['productName'] ?? ''); ?></td>
                                        <td><?php echo esc_html($order['date'] ?? ''); ?></td>
                                        <td>
                                            <button class="button delete-abandoned-order"
                                                    data-id="<?php echo esc_attr($order['id']); ?>">
                                                حذف
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php else: ?>
                            <p>لا توجد طلبات متروكة حالياً.</p>
                        <?php endif; ?>
                    </div>

                    <h2>إدارة الحظر</h2>
                    <div class="block-management">
                        <div class="add-block-form">
                            <h3>إضافة حظر جديد</h3>
                            <div class="form-group">
                                <label>نوع الحظر</label>
                                <select id="blockType" class="form-select">
                                    <option value="phone">رقم الهاتف</option>
                                    <option value="ip">عنوان IP</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>القيمة</label>
                                <input type="text" id="blockValue" class="form-input" placeholder="أدخل رقم الهاتف أو عنوان IP">
                            </div>
                            <div class="form-group">
                                <label>سبب الحظر (اختياري)</label>
                                <input type="text" id="blockReason" class="form-input" placeholder="سبب الحظر">
                            </div>
                            <button type="button" class="button add-block-button">إضافة حظر</button>
                        </div>

                        <div class="blocked-list">
                            <h3>القائمة السوداء</h3>
                            <?php
                            $blocked_items = get_option('custom_order_form_blocked_items', array());
                            if (!empty($blocked_items)): ?>
                                <table class="wp-list-table widefat fixed striped">
                                    <thead>
                                        <tr>
                                            <th>النوع</th>
                                            <th>القيمة</th>
                                            <th>تاريخ الحظر</th>
                                            <th>السبب</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($blocked_items as $item): ?>
                                        <tr>
                                            <td><?php echo $item['type'] === 'phone' ? 'رقم الهاتف' : 'عنوان IP'; ?></td>
                                            <td><?php echo esc_html($item['value']); ?></td>
                                            <td><?php echo esc_html($item['date']); ?></td>
                                            <td><?php echo esc_html($item['reason'] ?: '-'); ?></td>
                                            <td>
                                                <button class="button remove-block-button"
                                                        data-type="<?php echo esc_attr($item['type']); ?>"
                                                        data-value="<?php echo esc_attr($item['value']); ?>">
                                                    إلغاء الحظر
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php else: ?>
                                <p>لا توجد عناصر محظورة حالياً.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <h2>قائمة الزبائن</h2>
                    <div class="customers-list">
                        <?php
                        $customers = $wpdb->get_results("
                            SELECT
                                pm_name.meta_value as customer_name,
                                pm_phone.meta_value as phone,
                                COUNT(DISTINCT p.ID) as order_count,
                                MAX(p.post_date) as last_order,
                                GROUP_CONCAT(DISTINCT pm_ip.meta_value) as ip_addresses
                            FROM {$wpdb->postmeta} pm_name
                            JOIN {$wpdb->posts} p ON p.ID = pm_name.post_id
                            LEFT JOIN {$wpdb->postmeta} pm_phone ON pm_phone.post_id = p.ID AND pm_phone.meta_key = '_billing_phone'
                            LEFT JOIN {$wpdb->postmeta} pm_ip ON pm_ip.post_id = p.ID AND pm_ip.meta_key = '_customer_ip_address'
                            WHERE pm_name.meta_key = '_customer_full_name'
                            AND p.post_type = 'shop_order'
                            GROUP BY pm_name.meta_value, pm_phone.meta_value
                            ORDER BY last_order DESC
                        ");

                        // الحصول على قائمة العملاء المحظورين
                        $blocked_customers = get_option('custom_order_form_blocked_customers', array());

                        if (!empty($customers)): ?>
                            <table class="wp-list-table widefat fixed striped">
                                <thead>
                                    <tr>
                                        <th>اسم الزبون</th>
                                        <th>رقم الهاتف</th>
                                        <th>عدد الطلبات</th>
                                        <th>آخر طلب</th>
                                        <th>عناوين IP</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($customers as $customer):
                                        $is_blocked = in_array($customer->phone, $blocked_customers);
                                    ?>
                                    <tr>
                                        <td><?php echo esc_html($customer->customer_name); ?></td>
                                        <td><?php echo esc_html($customer->phone); ?></td>
                                        <td><?php echo esc_html($customer->order_count); ?></td>
                                        <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($customer->last_order))); ?></td>
                                        <td><?php echo esc_html($customer->ip_addresses); ?></td>
                                        <td>
                                            <button class="button <?php echo $is_blocked ? 'unblock-customer' : 'block-customer'; ?>"
                                                    data-phone="<?php echo esc_attr($customer->phone); ?>"
                                                    data-action="<?php echo $is_blocked ? 'unblock' : 'block'; ?>">
                                                <?php echo $is_blocked ? 'إلغاء الحظر' : 'حظر'; ?>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php else: ?>
                            <p>لا يوجد زبائن حالياً.</p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="settings-panel active" id="fields-panel">
                    <h2>إعدادات النص</h2>
                    <div class="form-group">
                        <label>عنوان النموذج</label>
                        <input type="text" name="form_title"
                               value="<?php echo esc_attr($form_title); ?>"
                               class="regular-text">
                        <p class="description">النص الذي يظهر في أعلى النموذج</p>
                    </div>

                    <h2>إعدادات التوصيل</h2>
                    <div class="form-group">
                        <label>سعر التوصيل الثابت</label>
                        <input type="number" name="shipping_settings[fixed_price]"
                               value="<?php echo esc_attr($shipping_settings['fixed_price']); ?>"
                               class="regular-text"
                               min="0"
                               step="0.01">
                        <p class="description">سيتم استخدام هذا السعر إذا كان حقل الولاية مخفياً</p>
                    </div>
                    <h2>إعدادات الواتساب</h2>
                    <div class="form-group">
                        <label>رقم الواتساب</label>
                        <input type="text" name="whatsapp_settings[number]"
                               value="<?php echo esc_attr($whatsapp_settings['number']); ?>"
                               class="regular-text"
                               placeholder="مثال: 213123456789">
                        <p class="description">أدخل رقم الواتساب مع رمز البلد (مثال: 213 للجزائر)</p>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="whatsapp_settings[enabled]"
                                   <?php checked($whatsapp_settings['enabled'], true); ?>>
                            تفعيل زر الطلب عبر الواتساب
                        </label>
                    </div>

                    <h2>إعدادات منع السبام</h2>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="spam_settings[disable_autocomplete]"
                                   <?php checked($spam_settings['disable_autocomplete'], true); ?>>
                            منع الإكمال التلقائي في خانات الفورم
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="spam_settings[disable_copy_paste]"
                                   <?php checked($spam_settings['disable_copy_paste'], true); ?>>
                            منع نسخ ولصق النص في خانات الفورم
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="spam_settings[limit_orders]"
                                   <?php checked($spam_settings['limit_orders'], true); ?>>
                            تفعيل خاصية ارسال طلبية واحدة فقط خلال 24 ساعة
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="spam_settings[save_abandoned]"
                                   <?php checked($spam_settings['save_abandoned'], true); ?>>
                            تفعيل خاصية حفظ الطلبات المتروكة
                        </label>
                    </div>

                    <h2>إعدادات زر الشراء</h2>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="button_settings[show_sticky_button]"
                                   <?php checked($button_settings['show_sticky_button'], true); ?>>
                            إظهار زر الشراء المثبت أسفل الصفحة
                        </label>
                    </div>
                    <div class="form-group">
                        <label>نص زر الشراء</label>
                        <input type="text" name="button_settings[button_text]"
                               value="<?php echo esc_attr($button_settings['button_text']); ?>"
                               class="regular-text"
                               placeholder="اشتري الآن">
                    </div>

                    <h2>إظهار/إخفاء الحقول</h2>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="field_visibility[show_address]"
                                   <?php checked($field_visibility['show_address'], true); ?>>
                            إظهار حقل العنوان
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="field_visibility[show_state]"
                                   <?php checked($field_visibility['show_state'], true); ?>>
                            إظهار حقل الولاية
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="field_visibility[show_municipality]"
                                   <?php checked($field_visibility['show_municipality'], true); ?>>
                            إظهار حقل البلدية
                        </label>
                    </div>

                    <h2>تسميات الحقول</h2>
                    <div class="form-group">
                        <label>الاسم الكامل</label>
                        <input type="text" name="field_labels[fullName]"
                               value="<?php echo esc_attr($field_labels['fullName']); ?>" class="regular-text">
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="text" name="field_labels[phone]"
                               value="<?php echo esc_attr($field_labels['phone']); ?>" class="regular-text">
                    </div>
                    <div class="form-group">
                        <label>العنوان</label>
                        <input type="text" name="field_labels[address]"
                               value="<?php echo esc_attr($field_labels['address']); ?>" class="regular-text">
                    </div>
                    <div class="form-group">
                        <label>الولاية</label>
                        <input type="text" name="field_labels[state]"
                               value="<?php echo esc_attr($field_labels['state']); ?>" class="regular-text">
                    </div>
                    <div class="form-group">
                        <label>البلدية</label>
                        <input type="text" name="field_labels[municipality]"
                               value="<?php echo esc_attr($field_labels['municipality']); ?>" class="regular-text">
                    </div>

                    <h2>إعدادات الألوان</h2>
                    <div class="form-group">
                        <label>الألوان المتاحة للمتغيرات</label>
                        <table class="wp-list-table widefat fixed color-settings-table">
                            <thead>
                                <tr>
                                    <th width="25%">اسم اللون</th>
                                    <th width="25%">قيمة اللون</th>
                                    <th width="50%">معاينة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $saved_colors = get_option('custom_order_form_colors', array(
                                    'أحمر' => '#ff0000',
                                    'أخضر' => '#00ff00',
                                    'أزرق' => '#0000ff',
                                    'أسود' => '#000000',
                                    'أبيض' => '#ffffff',
                                    'أصفر' => '#ffff00',
                                    'برتقالي' => '#ffa500',
                                    'بني' => '#a52a2a',
                                    'رمادي' => '#808080',
                                    'ذهبي' => '#ffd700',
                                    'فضي' => '#c0c0c0',
                                    'وردي' => '#ffc0cb',
                                    'بنفسجي' => '#800080'
                                ));

                                foreach ($saved_colors as $name => $value):
                                ?>
                                <tr>
                                    <td>
                                        <input type="text"
                                               name="colors[names][]"
                                               value="<?php echo esc_attr($name); ?>"
                                               class="regular-text color-name">
                                    </td>
                                    <td>
                                        <input type="color"
                                               name="colors[values][]"
                                               value="<?php echo esc_attr($value); ?>"
                                               class="color-picker">
                                    </td>
                                    <td>
                                        <div class="color-preview" style="background-color: <?php echo esc_attr($value); ?>"></div>
                                        <button type="button" class="button delete-color">حذف</button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <button type="button" class="button add-new-color">إضافة لون جديد</button>
                    </div>

                    <h2>إعدادات صفحة الشكر</h2>
                    <div class="form-group">
                        <label>رابط صفحة الشكر المخصصة</label>
                        <input type="url"
                               name="thank_you_page"
                               value="<?php echo esc_url(get_option('custom_order_form_thank_you_page', '')); ?>"
                               class="regular-text"
                               placeholder="https://example.com/thank-you">
                        <p class="description">أدخل الرابط الكامل لصفحة الشكر المخصصة. اتركه فارغاً لاستخدام الصفحة الافتراضية.</p>
                    </div>
                </div>

                <div class="settings-panel" id="design-panel">
                    <h2>إعدادات التصميم</h2>
                    <div class="form-group">
                        <label>اللون الرئيسي</label>
                        <input type="color" name="design[primaryColor]"
                               value="<?php echo esc_attr($design_settings['primaryColor']); ?>" class="color-picker">
                    </div>
                    <div class="form-group">
                        <label>لون الأزرار</label>
                        <input type="color" name="design[buttonColor]"
                               value="<?php echo esc_attr($design_settings['buttonColor']); ?>" class="color-picker">
                    </div>
                    <div class="form-group">
                        <label>نوع الخط</label>
                        <select name="design[fontFamily]">
                            <option value="IBM Plex Sans Arabic" <?php selected($design_settings['fontFamily'], 'IBM Plex Sans Arabic'); ?>>IBM Plex Sans Arabic</option>
                            <option value="Tajawal" <?php selected($design_settings['fontFamily'], 'Tajawal'); ?>>Tajawal</option>
                            <option value="Cairo" <?php selected($design_settings['fontFamily'], 'Cairo'); ?>>Cairo</option>
                        </select>
                    </div>

                    <h2>تنسيق النموذج</h2>
                    <div class="form-group">
                        <label>لون الخلفية</label>
                        <input type="color" name="design[backgroundColor]"
                               value="<?php echo esc_attr($design_settings['backgroundColor'] ?? '#ffffff'); ?>"
                               class="color-picker">
                    </div>
                    <div class="form-group">
                        <label>لون النص</label>
                        <input type="color" name="design[textColor]"
                               value="<?php echo esc_attr($design_settings['textColor'] ?? '#1f2937'); ?>"
                               class="color-picker">
                    </div>
                    <div class="form-group">
                        <label>لون الحدود</label>
                        <input type="color" name="design[borderColor]"
                               value="<?php echo esc_attr($design_settings['borderColor'] ?? '#e2e8f0'); ?>"
                               class="color-picker">
                    </div>
                    <div class="form-group">
                        <label>نصف قطر الحواف (بالبكسل)</label>
                        <input type="number" name="design[borderRadius]"
                               value="<?php echo esc_attr($design_settings['borderRadius'] ?? '12'); ?>"
                               class="small-text"
                               min="0"
                               max="50">
                    </div>
                </div>

                <div class="settings-panel" id="shipping-panel">
                    <h2>إعدادات التوصيل</h2>
                    <div class="form-group">
                        <label>سعر التوصيل الثابت</label>
                        <input type="number" name="shipping_settings[fixed_price]"
                               value="<?php echo esc_attr($shipping_settings['fixed_price']); ?>"
                               class="regular-text"
                               min="0"
                               step="0.01">
                        <p class="description">سيتم استخدام هذا السعر إذا كان حقل الولاية مخفياً</p>
                    </div>

                    <h2>أسعار التوصيل حسب الولاية</h2>
                    <p class="description">قم بتعديل أسعار التوصيل للمنزل والمكتب لكل ولاية</p>

                    <div class="shipping-prices-container">
                        <table class="wp-list-table widefat fixed striped shipping-prices-table">
                            <thead>
                                <tr>
                                    <th width="50%">الولاية</th>
                                    <th width="25%">سعر التوصيل للمنزل</th>
                                    <th width="25%">سعر التوصيل للمكتب</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // قائمة الولايات
                                $states = array(
                                    "01: ولاية أدرار", "02: ولاية الشلف", "03: ولاية الأغواط", "04: ولاية أم البواقي", "05: ولاية باتنة",
                                    "06: ولاية بجاية", "07: ولاية بسكرة", "08: ولاية بشار", "09: ولاية البليدة", "10: ولاية البويرة",
                                    "11: ولاية تمنراست", "12: ولاية تبسة", "13: ولاية تلمسان", "14: ولاية تيارت", "15: ولاية تيزي وزو",
                                    "16: ولاية الجزائر", "17: ولاية الجلفة", "18: ولاية جيجل", "19: ولاية سطيف", "20: ولاية سعيدة",
                                    "21: ولاية سكيكدة", "22: ولاية سيدي بلعباس", "23: ولاية عنابة", "24: ولاية قالمة", "25: ولاية قسنطينة",
                                    "26: ولاية المدية", "27: ولاية مستغانم", "28: ولاية المسيلة", "29: ولاية معسكر", "30: ولاية ورقلة",
                                    "31: ولاية وهران", "32: ولاية البيض", "33: ولاية إليزي", "34: ولاية برج بوعريريج", "35: ولاية بومرداس",
                                    "36: ولاية الطارف", "37: ولاية تندوف", "38: ولاية تيسمسيلت", "39: ولاية الوادي", "40: ولاية خنشلة",
                                    "41: ولاية سوق أهراس", "42: ولاية تيبازة", "43: ولاية ميلة", "44: ولاية عين الدفلى", "45: ولاية النعامة",
                                    "46: ولاية عين تموشنت", "47: ولاية غرداية", "48: ولاية غليزان", "49: ولاية تيميمون", "50: ولاية برج باجي مختار",
                                    "51: ولاية أولاد جلال", "52: ولاية بني عباس", "53: ولاية عين صالح", "54: ولاية عين قزام", "55: ولاية تقرت",
                                    "56: ولاية جانت", "57: ولاية المغير", "58: ولاية المنيعة"
                                );

                                // القيم الافتراضية لأسعار التوصيل
                                $default_prices = array(
                                    "01: ولاية أدرار" => array("home" => 1300, "office" => 900),
                                    "02: ولاية الشلف" => array("home" => 800, "office" => 500),
                                    "03: ولاية الأغواط" => array("home" => 950, "office" => 600),
                                    "04: ولاية أم البواقي" => array("home" => 800, "office" => 500),
                                    "05: ولاية باتنة" => array("home" => 800, "office" => 500),
                                    "06: ولاية بجاية" => array("home" => 800, "office" => 500),
                                    "07: ولاية بسكرة" => array("home" => 950, "office" => 700),
                                    "08: ولاية بشار" => array("home" => 1250, "office" => 700),
                                    "09: ولاية البليدة" => array("home" => 600, "office" => 400),
                                    "10: ولاية البويرة" => array("home" => 750, "office" => 500),
                                    "11: ولاية تمنراست" => array("home" => 1500, "office" => 1050),
                                    "12: ولاية تبسة" => array("home" => 900, "office" => 500),
                                    "13: ولاية تلمسان" => array("home" => 850, "office" => 500),
                                    "14: ولاية تيارت" => array("home" => 800, "office" => 500),
                                    "15: ولاية تيزي وزو" => array("home" => 850, "office" => 500),
                                    "16: ولاية الجزائر" => array("home" => 650, "office" => 350),
                                    "17: ولاية الجلفة" => array("home" => 850, "office" => 500),
                                    "18: ولاية جيجل" => array("home" => 800, "office" => 500),
                                    "19: ولاية سطيف" => array("home" => 850, "office" => 500),
                                    "20: ولاية سعيدة" => array("home" => 850, "office" => 500),
                                    "21: ولاية سكيكدة" => array("home" => 850, "office" => 500),
                                    "22: ولاية سيدي بلعباس" => array("home" => 850, "office" => 500),
                                    "23: ولاية عنابة" => array("home" => 850, "office" => 500),
                                    "24: ولاية قالمة" => array("home" => 850, "office" => 500),
                                    "25: ولاية قسنطينة" => array("home" => 850, "office" => 500),
                                    "26: ولاية المدية" => array("home" => 500, "office" => 300),
                                    "27: ولاية مستغانم" => array("home" => 850, "office" => 500),
                                    "28: ولاية المسيلة" => array("home" => 800, "office" => 500),
                                    "29: ولاية معسكر" => array("home" => 800, "office" => 500),
                                    "30: ولاية ورقلة" => array("home" => 1000, "office" => 600),
                                    "31: ولاية وهران" => array("home" => 800, "office" => 500),
                                    "32: ولاية البيض" => array("home" => 1000, "office" => 600),
                                    "33: ولاية إليزي" => array("home" => 2000, "office" => 1500),
                                    "34: ولاية برج بوعريريج" => array("home" => 800, "office" => 500),
                                    "35: ولاية بومرداس" => array("home" => 800, "office" => 500),
                                    "36: ولاية الطارف" => array("home" => 850, "office" => 500),
                                    "37: ولاية تندوف" => array("home" => 2000, "office" => 1800),
                                    "38: ولاية تيسمسيلت" => array("home" => 750, "office" => 750),
                                    "39: ولاية الوادي" => array("home" => 950, "office" => 600),
                                    "40: ولاية خنشلة" => array("home" => 850, "office" => 850),
                                    "41: ولاية سوق أهراس" => array("home" => 850, "office" => 500),
                                    "42: ولاية تيبازة" => array("home" => 800, "office" => 500),
                                    "43: ولاية ميلة" => array("home" => 850, "office" => 500),
                                    "44: ولاية عين الدفلى" => array("home" => 850, "office" => 500),
                                    "45: ولاية النعامة" => array("home" => 1200, "office" => 600),
                                    "46: ولاية عين تموشنت" => array("home" => 850, "office" => 500),
                                    "47: ولاية غرداية" => array("home" => 950, "office" => 600),
                                    "48: ولاية غليزان" => array("home" => 850, "office" => 500),
                                    "49: ولاية تيميمون" => array("home" => 1400, "office" => 1400),
                                    "50: ولاية برج باجي مختار" => array("home" => 2000, "office" => 2000),
                                    "51: ولاية أولاد جلال" => array("home" => 950, "office" => 600),
                                    "52: ولاية بني عباس" => array("home" => 1400, "office" => 1400),
                                    "53: ولاية عين صالح" => array("home" => 1600, "office" => 1600),
                                    "54: ولاية عين قزام" => array("home" => 1600, "office" => 1600),
                                    "55: ولاية تقرت" => array("home" => 950, "office" => 600),
                                    "56: ولاية جانت" => array("home" => 2000, "office" => 2000),
                                    "57: ولاية المغير" => array("home" => 950, "office" => 950),
                                    "58: ولاية المنيعة" => array("home" => 1000, "office" => 1000)
                                );

                                // دمج الأسعار المخصصة مع الأسعار الافتراضية
                                $shipping_prices = !empty($shipping_prices) ? $shipping_prices : $default_prices;

                                foreach ($states as $state) {
                                    $home_price = isset($shipping_prices[$state]['home']) ? $shipping_prices[$state]['home'] : $default_prices[$state]['home'];
                                    $office_price = isset($shipping_prices[$state]['office']) ? $shipping_prices[$state]['office'] : $default_prices[$state]['office'];
                                    ?>
                                    <tr>
                                        <td><?php echo esc_html($state); ?></td>
                                        <td>
                                            <input type="number"
                                                   name="shipping_prices[<?php echo esc_attr($state); ?>][home]"
                                                   value="<?php echo esc_attr($home_price); ?>"
                                                   min="0"
                                                   step="1"
                                                   class="small-text">
                                            د.ج
                                            <label class="disable-shipping">
                                                <input type="checkbox"
                                                       class="disable-home-shipping"
                                                       data-state="<?php echo esc_attr($state); ?>"
                                                       <?php echo ($home_price === 0) ? 'checked' : ''; ?>>
                                                تعطيل
                                            </label>
                                        </td>
                                        <td>
                                            <input type="number"
                                                   name="shipping_prices[<?php echo esc_attr($state); ?>][office]"
                                                   value="<?php echo esc_attr($office_price); ?>"
                                                   min="0"
                                                   step="1"
                                                   class="small-text">
                                            د.ج
                                            <label class="disable-shipping">
                                                <input type="checkbox"
                                                       class="disable-office-shipping"
                                                       data-state="<?php echo esc_attr($state); ?>"
                                                       <?php echo ($office_price === 0) ? 'checked' : ''; ?>>
                                                تعطيل
                                            </label>
                                        </td>
                                    </tr>
                                    <?php
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="form-group" style="margin-top: 20px;">
                        <div class="csv-import-export">
                            <h3>استيراد/تصدير أسعار التوصيل</h3>
                            <div class="csv-buttons">
                                <button type="button" class="button download-csv-template" id="download-csv-btn">تنزيل نموذج CSV</button>
                                <div class="csv-upload-wrapper">
                                    <button type="button" class="button upload-csv-button" id="upload-csv-btn">رفع ملف CSV</button>
                                    <input type="file" id="csv-upload" accept=".csv" style="display: none;">
                                </div>
                            </div>
                            <div class="csv-instructions">
                                <p>يمكنك تنزيل نموذج CSV يحتوي على جميع الولايات وأسعار التوصيل الحالية، ثم تعديله ورفعه مرة أخرى.</p>
                                <p>تنسيق الملف: <code>الولاية,سعر التوصيل للمنزل,سعر التوصيل للمكتب</code></p>
                                <p>ملاحظة: استخدم القيمة 0 لتعطيل التوصيل لنوع معين.</p>
                            </div>
                        </div>
                        <div id="csv-upload-result" style="margin-top: 10px;"></div>
                    </div>

                    <script>
                    jQuery(document).ready(function($) {
                        // تنزيل نموذج CSV
                        $('#download-csv-btn').on('click', function() {
                            console.log('تم النقر على زر التنزيل');
                            const downloadUrl = '<?php echo admin_url('admin-ajax.php'); ?>?action=download_shipping_csv&nonce=<?php echo wp_create_nonce('custom_order_form_admin_nonce'); ?>';
                            window.open(downloadUrl, '_blank');
                        });

                        // رفع ملف CSV
                        $('#upload-csv-btn').on('click', function() {
                            console.log('تم النقر على زر الرفع');
                            $('#csv-upload').click();
                        });

                        $('#csv-upload').on('change', function(e) {
                            console.log('تم اختيار ملف');
                            const file = e.target.files[0];
                            if (!file) return;

                            // التحقق من نوع الملف
                            if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
                                showCsvResult('error', 'يرجى اختيار ملف CSV صالح');
                                return;
                            }

                            const reader = new FileReader();
                            reader.onload = function(event) {
                                processCsvFile(event.target.result);
                            };
                            reader.onerror = function() {
                                showCsvResult('error', 'حدث خطأ أثناء قراءة الملف');
                            };
                            reader.readAsText(file);
                        });

                        // معالجة ملف CSV
                        function processCsvFile(csvContent) {
                            try {
                                // تقسيم المحتوى إلى أسطر
                                const lines = csvContent.split(/\r\n|\n/);
                                if (lines.length < 2) {
                                    showCsvResult('error', 'الملف فارغ أو لا يحتوي على بيانات كافية');
                                    return;
                                }

                                // التحقق من العنوان (الصف الأول)
                                const header = lines[0].split(',');
                                if (header.length < 3) {
                                    showCsvResult('error', 'تنسيق الملف غير صحيح. يجب أن يحتوي على ثلاثة أعمدة: الولاية، سعر التوصيل للمنزل، سعر التوصيل للمكتب');
                                    return;
                                }

                                let updatedCount = 0;
                                let errors = [];

                                // معالجة كل سطر (بدءًا من السطر الثاني)
                                for (let i = 1; i < lines.length; i++) {
                                    if (!lines[i].trim()) continue; // تخطي الأسطر الفارغة

                                    const values = lines[i].split(',');
                                    if (values.length < 3) {
                                        errors.push(`السطر ${i+1}: عدد الأعمدة غير كافٍ`);
                                        continue;
                                    }

                                    const state = values[0].trim();
                                    const homePrice = parseInt(values[1].trim());
                                    const officePrice = parseInt(values[2].trim());

                                    // التحقق من صحة البيانات
                                    if (isNaN(homePrice) || isNaN(officePrice) || homePrice < 0 || officePrice < 0) {
                                        errors.push(`السطر ${i+1}: قيم الأسعار غير صالحة`);
                                        continue;
                                    }

                                    // البحث عن حقول الإدخال المناسبة وتحديث قيمها
                                    const homeInput = $(`input[name="shipping_prices[${state}][home]"]`);
                                    const officeInput = $(`input[name="shipping_prices[${state}][office]"]`);

                                    if (homeInput.length && officeInput.length) {
                                        homeInput.val(homePrice);
                                        officeInput.val(officePrice);

                                        // تحديث حالة خانات الاختيار
                                        $(`.disable-home-shipping[data-state="${state}"]`).prop('checked', homePrice === 0);
                                        $(`.disable-office-shipping[data-state="${state}"]`).prop('checked', officePrice === 0);

                                        // تعطيل حقول الإدخال إذا كانت القيمة 0
                                        homeInput.prop('disabled', homePrice === 0);
                                        officeInput.prop('disabled', officePrice === 0);

                                        updatedCount++;
                                    } else {
                                        errors.push(`السطر ${i+1}: لم يتم العثور على الولاية "${state}"`);
                                    }
                                }

                                // عرض نتيجة المعالجة
                                if (errors.length > 0) {
                                    showCsvResult('error', `تم تحديث ${updatedCount} ولاية، ولكن هناك ${errors.length} أخطاء:<br>` + errors.join('<br>'));
                                } else {
                                    showCsvResult('success', `تم تحديث أسعار التوصيل لـ ${updatedCount} ولاية بنجاح`);
                                }

                            } catch (error) {
                                console.error('Error processing CSV:', error);
                                showCsvResult('error', 'حدث خطأ أثناء معالجة الملف: ' + error.message);
                            }
                        }

                        // عرض نتيجة معالجة ملف CSV
                        function showCsvResult(type, message) {
                            const resultElement = $('#csv-upload-result');
                            resultElement.removeClass('success error').addClass(type);
                            resultElement.html(message);

                            // إخفاء الرسالة بعد 10 ثوانٍ
                            setTimeout(() => {
                                resultElement.fadeOut(500, function() {
                                    $(this).html('').removeClass('success error').show();
                                });
                            }, 10000);
                        }
                    });
                    </script>

                    <div class="form-group" style="margin-top: 20px;">
                        <button type="button" class="button reset-shipping-prices">إعادة تعيين الأسعار الافتراضية</button>
                    </div>
                </div>

                <button type="submit" class="button button-primary">حفظ الإعدادات</button>
            </form>
        </div>
        <?php
    }

    // إضافة metabox لصور المتغيرات
    public function add_variation_images_metabox() {
        add_meta_box(
            'variation_images',
            'صور المتغيرات',
            array($this, 'render_variation_images_metabox'),
            'product',
            'normal',
            'high'
        );

        // إضافة الوسائط
        wp_enqueue_media();
    }

    // عرض محتوى metabox صور المتغيرات
    public function render_variation_images_metabox($post) {
        wp_nonce_field('variation_images_metabox', 'variation_images_nonce');

        // الحصول على المتغيرات الحالية للمنتج
        $product = wc_get_product($post->ID);
        if (!$product || !$product->is_type('variable')) {
            echo '<p>هذا المنتج ليس متغيراً. أضف متغيرات للمنتج أولاً.</p>';
            return;
        }

        $attributes = $product->get_variation_attributes();
        $saved_images = get_post_meta($post->ID, '_variation_images', true) ?: array();
        ?>
        <div class="variation-images-container">
            <?php foreach ($attributes as $attribute_name => $options): ?>
                <div class="attribute-images">
                    <h3><?php echo wc_attribute_label($attribute_name); ?></h3>
                    <?php foreach ($options as $option): ?>
                        <div class="variation-image-row">
                            <label><?php echo esc_html($option); ?></label>
                            <div class="image-upload-field">
                                <?php
                                $image_id = isset($saved_images[$attribute_name][$option]) ?
                                          $saved_images[$attribute_name][$option] : '';
                                $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'thumbnail') : '';
                                ?>
                                <div class="image-preview">
                                    <?php if ($image_url): ?>
                                        <img src="<?php echo esc_url($image_url); ?>" alt="">
                                    <?php endif; ?>
                                </div>
                                <input type="hidden"
                                       name="variation_images[<?php echo esc_attr($attribute_name); ?>][<?php echo esc_attr($option); ?>]"
                                       value="<?php echo esc_attr($image_id); ?>"
                                       class="variation-image-id">
                                <button type="button" class="button upload-variation-image">
                                    <?php echo $image_id ? 'تغيير الصورة' : 'إضافة صورة'; ?>
                                </button>
                                <?php if ($image_id): ?>
                                    <button type="button" class="button remove-variation-image">حذف</button>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endforeach; ?>
        </div>

        <style>
            .variation-images-container {
                margin: 15px 0;
            }
            .attribute-images {
                margin-bottom: 20px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 5px;
            }
            .variation-image-row {
                display: flex;
                align-items: center;
                margin: 10px 0;
                padding: 10px;
                background: white;
                border-radius: 4px;
            }
            .variation-image-row label {
                min-width: 150px;
                margin-left: 15px;
            }
            .image-upload-field {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .image-preview {
                width: 60px;
                height: 60px;
                border: 1px solid #ddd;
                border-radius: 4px;
                overflow: hidden;
            }
            .image-preview img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        </style>

        <script>
        jQuery(document).ready(function($) {
            $('.upload-variation-image').click(function(e) {
                e.preventDefault();
                const button = $(this);
                const container = button.closest('.image-upload-field');
                const preview = container.find('.image-preview');
                const input = container.find('.variation-image-id');
                const removeButton = container.find('.remove-variation-image');

                const frame = wp.media({
                    title: 'اختر صورة للمتغير',
                    button: {
                        text: 'استخدم هذه الصورة'
                    },
                    multiple: false
                });

                frame.on('select', function() {
                    const attachment = frame.state().get('selection').first().toJSON();
                    preview.html(`<img src="${attachment.url}" alt="">`);
                    input.val(attachment.id);
                    button.text('تغيير الصورة');

                    if (!removeButton.length) {
                        container.append('<button type="button" class="button remove-variation-image">حذف</button>');
                    }
                });

                frame.open();
            });

            $(document).on('click', '.remove-variation-image', function() {
                const container = $(this).closest('.image-upload-field');
                container.find('.image-preview').empty();
                container.find('.variation-image-id').val('');
                container.find('.upload-variation-image').text('إضافة صورة');
                $(this).remove();
            });
        });
        </script>
        <?php
    }

    // حفظ صور المتغيرات
    public function save_variation_images_metabox($post_id) {
        if (!isset($_POST['variation_images_nonce']) ||
            !wp_verify_nonce($_POST['variation_images_nonce'], 'variation_images_metabox')) {
            return;
        }

        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        if (isset($_POST['variation_images'])) {
            $images = array_map(function($attribute) {
                return array_filter($attribute); // إزالة القيم الفارغة
            }, $_POST['variation_images']);

            update_post_meta($post_id, '_variation_images', $images);
        }
    }

    // إضافة metabox لزر السلة
    public function add_cart_button_metabox() {
        add_meta_box(
            'show_cart_button',
            'زر إضافة للسلة',
            array($this, 'render_cart_button_metabox'),
            'product',
            'side',
            'default'
        );
    }

    // عرض محتوى metabox
    public function render_cart_button_metabox($post) {
        wp_nonce_field('cart_button_metabox', 'cart_button_nonce');

        $show_cart = get_post_meta($post->ID, '_show_cart_button', true);
        ?>
        <div class="cart-button-settings">
            <label>
                <input type="checkbox"
                       name="show_cart_button"
                       value="yes"
                       <?php checked($show_cart, 'yes'); ?>>
                إظهار زر إضافة للسلة
            </label>
            <p class="description">
                عند تفعيل هذا الخيار، سيظهر زر إضافة للسلة في نموذج الطلب لهذا المنتج
            </p>
        </div>
        <?php
    }

    // حفظ إعدادات زر السلة
    public function save_cart_button_metabox($post_id) {
        // التحقق من الnonce
        if (!isset($_POST['cart_button_nonce']) ||
            !wp_verify_nonce($_POST['cart_button_nonce'], 'cart_button_metabox')) {
            return;
        }

        // التحقق من الصلاحيات
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // حفظ الإعداد
        if (isset($_POST['show_cart_button'])) {
            update_post_meta($post_id, '_show_cart_button', 'yes');
        } else {
            delete_post_meta($post_id, '_show_cart_button');
        }
    }

    public function save_form_settings() {
        check_ajax_referer('custom_order_form_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء');
            return;
        }

        // حفظ الألوان
        if (isset($_POST['colors']) && is_array($_POST['colors'])) {
            $colors = array();
            $names = isset($_POST['colors']['names']) ? (array)$_POST['colors']['names'] : array();
            $values = isset($_POST['colors']['values']) ? (array)$_POST['colors']['values'] : array();

            foreach ($names as $index => $name) {
                if (!empty($name) && isset($values[$index])) {
                    // تنظيف وتحقق من صحة القيم
                    $name = sanitize_text_field($name);
                    $value = preg_match('/^#[a-f0-9]{6}$/i', $values[$index]) ? $values[$index] : '#000000';
                    $colors[$name] = $value;
                }
            }

            // إذا لم يتم إرسال أي ألوان، نستخدم الألوان الافتراضية
            if (empty($colors)) {
                $colors = array(
                    'أحمر' => '#ff0000',
                    'أخضر' => '#00ff00',
                    'أزرق' => '#0000ff',
                    'أسود' => '#000000',
                    'أبيض' => '#ffffff',
                    'أصفر' => '#ffff00',
                    'برتقالي' => '#ffa500',
                    'بني' => '#a52a2a',
                    'رمادي' => '#808080',
                    'ذهبي' => '#ffd700',
                    'فضي' => '#c0c0c0',
                    'وردي' => '#ffc0cb',
                    'بنفسجي' => '#800080'
                );
            }

            update_option('custom_order_form_colors', $colors);
        }

        $form_title = isset($_POST['form_title']) ? sanitize_text_field($_POST['form_title']) : '';
        $field_labels = isset($_POST['field_labels']) ? $_POST['field_labels'] : array();
        $field_visibility = isset($_POST['field_visibility']) ? array(
            'show_address' => isset($_POST['field_visibility']['show_address']),
            'show_state' => isset($_POST['field_visibility']['show_state']),
            'show_municipality' => isset($_POST['field_visibility']['show_municipality'])
        ) : array();

        $whatsapp_settings = isset($_POST['whatsapp_settings']) ? array(
            'number' => sanitize_text_field($_POST['whatsapp_settings']['number']),
            'enabled' => isset($_POST['whatsapp_settings']['enabled'])
        ) : array();

        $button_settings = isset($_POST['button_settings']) ? array(
            'show_sticky_button' => isset($_POST['button_settings']['show_sticky_button']),
            'button_text' => sanitize_text_field($_POST['button_settings']['button_text'])
        ) : array();

        $spam_settings = isset($_POST['spam_settings']) ? array(
            'disable_autocomplete' => isset($_POST['spam_settings']['disable_autocomplete']),
            'disable_copy_paste' => isset($_POST['spam_settings']['disable_copy_paste']),
            'limit_orders' => isset($_POST['spam_settings']['limit_orders']),
            'save_abandoned' => isset($_POST['spam_settings']['save_abandoned'])
        ) : array();
        $design_settings = isset($_POST['design']) ? $_POST['design'] : array();
        $shipping_settings = isset($_POST['shipping_settings']) ? array(
            'fixed_price' => floatval($_POST['shipping_settings']['fixed_price']),
            'use_fixed_price' => !isset($_POST['field_visibility']['show_state'])
        ) : array();

        // حفظ أسعار التوصيل المخصصة
        $shipping_prices = isset($_POST['shipping_prices']) ? $_POST['shipping_prices'] : array();

        // تنظيف وتحقق من صحة القيم
        $clean_shipping_prices = array();
        if (!empty($shipping_prices)) {
            foreach ($shipping_prices as $state => $prices) {
                $clean_shipping_prices[$state] = array(
                    'home' => isset($prices['home']) ? max(0, intval($prices['home'])) : 0,
                    'office' => isset($prices['office']) ? max(0, intval($prices['office'])) : 0
                );
            }
        }

        update_option('custom_order_form_title', $form_title);
        update_option('custom_order_form_field_labels', $field_labels);
        update_option('custom_order_form_field_visibility', $field_visibility);
        update_option('custom_order_form_whatsapp_settings', $whatsapp_settings);
        update_option('custom_order_form_button_settings', $button_settings);
        update_option('custom_order_form_spam_settings', $spam_settings);
        update_option('custom_order_form_design', $design_settings);
        update_option('custom_order_form_shipping_settings', $shipping_settings);
        update_option('custom_order_form_shipping_prices', $clean_shipping_prices);

        // حفظ إعدادات صفحة الشكر
        $thank_you_page = isset($_POST['thank_you_page']) ? esc_url_raw($_POST['thank_you_page']) : '';
        update_option('custom_order_form_thank_you_page', $thank_you_page);

        wp_send_json_success(array(
            'message' => 'تم حفظ الإعدادات بنجاح',
            'settings' => array(
                'fieldLabels' => $field_labels,
                'fieldVisibility' => $field_visibility,
                'whatsapp_number' => $whatsapp_settings['number'],
                'whatsapp_enabled' => $whatsapp_settings['enabled'],
                'design' => $design_settings
            )
        ));
    }
}