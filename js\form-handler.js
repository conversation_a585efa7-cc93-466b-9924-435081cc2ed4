// تحديث صورة المنتج الرئيسية عند اختيار متغير
function updateMainProductImage(imageUrl) {
    const mainImage = document.querySelector('.woocommerce-product-gallery__image img');
    if (mainImage && imageUrl) {
        mainImage.src = imageUrl;
        mainImage.srcset = ''; // إزالة srcset لمنع تحميل صور أخرى
    }
}

// معالجة اختيار المتغيرات
document.addEventListener('DOMContentLoaded', function() {
    // إضافة معالج النقر لجميع خيارات المتغيرات
    document.querySelectorAll('.swatch-option').forEach(option => {
        option.addEventListener('click', function() {
            // إزالة التحديد من جميع الخيارات في نفس المجموعة
            const attribute = this.dataset.attribute;
            document.querySelectorAll(`.swatch-option[data-attribute="${attribute}"]`)
                .forEach(el => el.classList.remove('selected'));
            
            // تحديد الخيار المختار
            this.classList.add('selected');
            
            // تحديث قيمة المتغير المخفي
            const input = document.querySelector(`input[name="${attribute}"]`);
            if (input) {
                input.value = this.dataset.value;
                input.dispatchEvent(new Event('change'));
            }

            // تحديث الصورة الرئيسية إذا كان هذا متغير صورة
            if (this.classList.contains('image-swatch')) {
                const imageUrl = this.dataset.imageUrl;
                if (imageUrl) {
                    updateMainProductImage(imageUrl);
                }
            }
        });
    });
});

// تطبيق إعدادات الحقول والتنسيق
function applyFormSettings() {
    const settings = window.woocommerce_params?.form_settings || {};
    const fieldVisibility = settings.fieldVisibility || {
        show_address: true,
        show_state: true,
        show_municipality: true
    };

    // تطبيق إظهار/إخفاء الحقول
    const addressField = document.querySelector('.form-group:has(#address)');
    const stateField = document.querySelector('.form-group:has(#country)');
    const municipalityField = document.querySelector('.form-group:has(#city)');

    if (addressField) {
        addressField.style.display = fieldVisibility.show_address ? 'block' : 'none';
    }
    if (stateField) {
        stateField.style.display = fieldVisibility.show_state ? 'block' : 'none';
    }
    if (municipalityField) {
        municipalityField.style.display = fieldVisibility.show_municipality ? 'block' : 'none';
    }

    // تحديث سعر الشحن الثابت إذا كان حقل الولاية مخفياً
    if (!fieldVisibility.show_state && settings.shipping?.fixed_price) {
        const shippingPriceElement = document.getElementById('shippingPrice');
        if (shippingPriceElement) {
            shippingPriceElement.textContent = parseFloat(settings.shipping.fixed_price).toFixed(2) + ' د.ج';
            updateTotalPrice();
        }
    }
}

// تطبيق إعدادات الواتساب
function applyWhatsAppSettings() {
    const settings = window.woocommerce_params?.form_settings || {};
    const whatsappButton = document.querySelector('.whatsapp-order-btn');
    if (whatsappButton) {
        whatsappButton.style.display = settings.whatsapp_enabled ? 'block' : 'none';
    }
}

// إرسال الطلب عبر واتساب
function orderViaWhatsApp() {
    const settings = window.woocommerce_params?.form_settings || {};
    const whatsappNumber = settings.whatsapp_number;
    
    if (!whatsappNumber) {
        alert('عذراً، رقم الواتساب غير متوفر حالياً');
        return;
    }

    const fullName = document.getElementById('fullName').value;
    if (!fullName) {
        alert('الرجاء إدخال الاسم');
        return;
    }

    const phone = document.getElementById('phone').value;
    if (!phone) {
        alert('الرجاء إدخال رقم الهاتف');
        return;
    }

    const address = document.getElementById('address')?.value || '';
    const country = document.getElementById('country')?.value || '';
    const city = document.getElementById('city')?.value || '';
    const quantity = document.getElementById('quantity').value;
    const productName = document.getElementById('productName').value;
    const deliveryType = document.querySelector('input[name="delivery_type"]:checked').value;
    const total = document.getElementById('totalPrice').textContent;

    let message = `*طلب جديد*%0a`;
    message += `المنتج: ${productName}%0a`;
    message += `الكمية: ${quantity}%0a`;
    message += `الاسم: ${fullName}%0a`;
    message += `الهاتف: ${phone}%0a`;
    
    const fieldVisibility = settings.fieldVisibility || {};

    if (fieldVisibility.show_state && country) {
        message += `الولاية: ${country}%0a`;
    }
    if (fieldVisibility.show_municipality && city) {
        message += `البلدية: ${city}%0a`;
    }
    if (fieldVisibility.show_address && address) {
        message += `العنوان: ${address}%0a`;
    }

    message += `نوع التوصيل: ${deliveryType === 'home' ? 'للمنزل' : 'للمكتب'}%0a`;
    message += `السعر الإجمالي: ${total}%0a`;

    window.open(`https://wa.me/${whatsappNumber}?text=${message}`, '_blank');
}

// تهيئة النموذج عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (!window.woocommerce_params?.form_settings) {
        console.error('Form settings not found');
        return;
    }

    applyFormSettings();
    applyWhatsAppSettings();
    
    // إضافة معالج النقر لزر الواتساب
    const whatsappButton = document.querySelector('.whatsapp-order-btn');
    if (whatsappButton) {
        whatsappButton.onclick = orderViaWhatsApp;
    }

    // معالجة نقر المتغيرات (الألوان، الأحجام، الصور)
    document.querySelectorAll('.swatch-option').forEach(swatch => {
        swatch.addEventListener('click', function() {
            const attribute = this.dataset.attribute;
            const value = this.dataset.value;
            const type = this.classList.contains('image-swatch') ? 'image' :
                        this.classList.contains('color-swatch') ? 'color' :
                        this.classList.contains('size-swatch') ? 'size' : 'text';

            // إزالة التحديد السابق
            document.querySelectorAll(`.swatch-option[data-attribute="${attribute}"]`)
                .forEach(el => el.classList.remove('selected'));
            
            // إضافة التحديد الجديد
            this.classList.add('selected');

            // تحديث قيمة المتغير المخفي
            const input = document.querySelector(`input[name="${attribute}"]`);
            if (input) {
                input.value = value;
                input.dispatchEvent(new Event('change'));
            }

            // إذا كان المتغير صورة، قم بتحديث صورة المنتج الرئيسية
            if (type === 'image') {
                const imageUrl = this.querySelector('img')?.src;
                if (imageUrl) {
                    const mainImage = document.querySelector('.woocommerce-product-gallery__image img');
                    if (mainImage) {
                        mainImage.src = imageUrl;
                        mainImage.srcset = ''; // إزالة srcset لمنع تحميل صور أخرى
                    }
                }
            }
        });
    });
});
