jQuery(document).ready(function($) {
    // إضافة زر إرسال الطلب
    $('.wc-proceed-to-checkout').html(
        '<button type="button" id="direct-order-submit" class="button alt">إرسال الطلب</button>'
    );

    // معالجة نقر زر إرسال الطلب
    $('#direct-order-submit').on('click', async function(e) {
        e.preventDefault();
        
        const button = $(this);
        const originalText = button.text();
        
        // تغيير نص الزر لإظهار حالة التحميل
        button.html('<i class="fas fa-spinner fa-spin"></i> جاري إرسال الطلب...');
        button.prop('disabled', true);

        try {
            const formData = new FormData();
            formData.append('action', 'place_direct_order');
            
            const response = await fetch(woocommerce_params.ajax_url, {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            });

            const data = await response.json();
            
            if (data.success) {
                // عرض رسالة النجاح
                alert('تم إرسال طلبك بنجاح!');
                // إعادة التوجيه إلى صفحة تأكيد الطلب
                window.location.href = data.data.redirect_url;
            } else {
                throw new Error(data.data || 'حدث خطأ أثناء إرسال الطلب');
            }
        } catch (error) {
            console.error('Error:', error);
            alert(error.message || 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
        } finally {
            // إعادة الزر إلى حالته الأصلية
            button.html(originalText);
            button.prop('disabled', false);
        }
    });
});